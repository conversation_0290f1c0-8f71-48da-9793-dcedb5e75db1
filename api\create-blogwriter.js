// api/create-blogwriter.js - Temporary endpoint to create a blogwriter user for testing
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const connectToDatabase = require('./connectToDatabase');

module.exports = async (req, res) => {
    // Only allow POST requests
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    try {
        await connectToDatabase();

        // Check if blogwriter user already exists
        const existingUser = await User.findOne({ role: 'blogwriter' });
        if (existingUser) {
            return res.status(200).json({
                message: 'Blogwriter user already exists',
                user: {
                    email: existingUser.email,
                    name: existingUser.name,
                    role: existingUser.role
                }
            });
        }

        // Create a test blogwriter user
        const email = '<EMAIL>';
        const password = 'blogwriter123'; // Simple password for testing
        const name = 'Test Blogwriter';

        // Hash the password
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Create the user
        const newUser = new User({
            name,
            email,
            password: hashedPassword,
            role: 'blogwriter'
        });

        const savedUser = await newUser.save();

        return res.status(201).json({
            message: 'Blogwriter user created successfully',
            user: {
                id: savedUser._id,
                email: savedUser.email,
                name: savedUser.name,
                role: savedUser.role
            },
            credentials: {
                email,
                password // Only return this for testing purposes
            }
        });

    } catch (error) {
        console.error('Error creating blogwriter user:', error);
        
        if (error.code === 11000) {
            return res.status(400).json({
                message: 'User with this email already exists'
            });
        }

        return res.status(500).json({
            message: 'Server error',
            error: error.message
        });
    }
};

// Tell Vercel we need the Node runtime
module.exports.config = { runtime: 'nodejs18.x' };
