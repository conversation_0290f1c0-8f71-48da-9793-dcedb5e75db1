<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Blog Overlays</title>
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/editor.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }

        .btn-primary {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        /* Mock blog card for testing */
        .mock-blog-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .mock-blog-hero {
            background: linear-gradient(rgba(0, 27, 68, 0.7), rgba(0, 87, 183, 0.7));
            color: white;
            padding: 40px 20px;
            text-align: center;
            position: relative;
            margin: 20px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>Blog Overlay Testing</h1>
    
    <div class="test-section">
        <h2>Test Environment</h2>
        <div id="test-results" class="test-results">
            <p>Loading test environment...</p>
        </div>
    </div>

    <div class="test-section">
        <h2>Mock Blog Elements</h2>
        
        <!-- Mock blog hero -->
        <div class="mock-blog-hero blog-hero-banner">
            <h1>Test Blog Hero</h1>
            <p>This should get a blog hero overlay</p>
        </div>
        
        <!-- Mock blog cards -->
        <div class="blog-grid-container">
            <div class="blog-card mock-blog-card">
                <h3>Test Blog Post 1</h3>
                <p>This is a test blog post excerpt...</p>
                <a href="/blog/test-id-1" class="blog-read-more">Read more</a>
            </div>
            
            <div class="blog-card mock-blog-card">
                <h3>Test Blog Post 2</h3>
                <p>This is another test blog post excerpt...</p>
                <a href="/blog/test-id-2" class="blog-read-more">Read more</a>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Authentication</h2>
        <p>To test blogwriter functionality, you need to be logged in as a blogwriter:</p>
        <a href="/login.html?role=blogwriter" class="btn btn-secondary">Login as Blogwriter</a>
        <a href="/blog" class="btn btn-secondary">Go to Blog Page</a>

        <h2>Test Controls</h2>
        <button id="test-user-role" class="btn btn-primary">Test User Role</button>
        <button id="test-blog-detection" class="btn btn-primary">Test Blog Detection</button>
        <button id="test-overlay-creation" class="btn btn-primary">Test Overlay Creation</button>
        <button id="test-routing" class="btn btn-primary">Test Routing</button>
        <div id="test-output" class="test-results"></div>
    </div>

    <!-- Visual Editor Scripts -->
    <script src="/js/editor/editor-state.js"></script>
    <script src="/js/editor/api-service.js"></script>
    <script src="/js/editor/override-engine.js"></script>
    <script src="/js/editor/features/image-browser.js"></script>
    <script src="/js/editor/features/section-sorter.js"></script>
    <script src="/js/editor/ui-manager.js"></script>
    <script src="/js/visual-editor-v2.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const testResults = document.getElementById('test-results');
            const testOutput = document.getElementById('test-output');
            
            // Test environment setup
            function updateTestResults(message, type = 'info') {
                const p = document.createElement('p');
                p.className = type;
                p.textContent = message;
                testResults.appendChild(p);
            }
            
            // Check if visual editor loaded
            setTimeout(async () => {
                if (window.visualEditor) {
                    updateTestResults('✅ Visual Editor loaded successfully', 'success');

                    if (window.visualEditor.uiManager) {
                        updateTestResults('✅ UI Manager available', 'success');

                        // Test blog detection
                        const isBlogPage = window.visualEditor.uiManager.isBlogPage();
                        updateTestResults(`Blog page detection: ${isBlogPage}`, isBlogPage ? 'success' : 'error');

                        // Test user role
                        if (window.visualEditor.userRole) {
                            const role = window.visualEditor.userRole;
                            updateTestResults(`User role: ${role.role || 'none'} (admin: ${role.isAdmin}, blogwriter: ${role.isBlogwriter})`,
                                role.isAdmin || role.isBlogwriter ? 'success' : 'info');
                        } else {
                            updateTestResults('User role not yet determined', 'info');
                        }

                        // Check if edit toggle button exists
                        const editToggle = document.getElementById('edit-mode-toggle');
                        updateTestResults(`Edit toggle button: ${editToggle ? 'Found' : 'NOT FOUND'}`,
                            editToggle ? 'success' : 'error');
                    } else {
                        updateTestResults('❌ UI Manager not available', 'error');
                    }
                } else {
                    updateTestResults('❌ Visual Editor not loaded', 'error');
                }
            }, 3000); // Increased timeout to allow for role checking

            // Test user role
            document.getElementById('test-user-role').addEventListener('click', async function() {
                testOutput.innerHTML = '';

                if (!window.apiService) {
                    testOutput.innerHTML = '<p class="error">API Service not available</p>';
                    return;
                }

                try {
                    // Test individual endpoints first
                    testOutput.innerHTML += '<p class="info">Testing authentication endpoints...</p>';

                    // Test admin endpoint
                    try {
                        const adminResp = await fetch('/api/login?check=admin', { cache: 'no-store' });
                        const adminData = adminResp.ok ? await adminResp.json() : null;
                        testOutput.innerHTML += `<p class="${adminResp.ok ? 'success' : 'error'}">Admin endpoint: ${adminResp.status} - ${adminData ? JSON.stringify(adminData) : 'Failed'}</p>`;
                    } catch (e) {
                        testOutput.innerHTML += `<p class="error">Admin endpoint error: ${e.message}</p>`;
                    }

                    // Test blogwriter endpoint
                    try {
                        const blogwriterResp = await fetch('/api/protected?role=blogwriter', { cache: 'no-store' });
                        const blogwriterData = blogwriterResp.ok ? await blogwriterResp.json() : null;
                        testOutput.innerHTML += `<p class="${blogwriterResp.ok ? 'success' : 'error'}">Blogwriter endpoint: ${blogwriterResp.status} - ${blogwriterData ? JSON.stringify(blogwriterData) : 'Failed'}</p>`;
                    } catch (e) {
                        testOutput.innerHTML += `<p class="error">Blogwriter endpoint error: ${e.message}</p>`;
                    }

                    // Test combined role check
                    const userRole = await window.apiService.checkUserRole();
                    testOutput.innerHTML += `
                        <p class="info">Combined Role Check Results:</p>
                        <p class="${userRole.role ? 'success' : 'error'}">Role: ${userRole.role || 'none'}</p>
                        <p class="${userRole.isAdmin ? 'success' : 'info'}">Is Admin: ${userRole.isAdmin}</p>
                        <p class="${userRole.isBlogwriter ? 'success' : 'info'}">Is Blogwriter: ${userRole.isBlogwriter}</p>
                        <p class="info">Blog overlays should ${userRole.isBlogwriter ? 'be visible' : 'NOT be visible'}</p>
                    `;
                } catch (error) {
                    testOutput.innerHTML += `<p class="error">Role check failed: ${error.message}</p>`;
                }
            });

            // Test blog detection
            document.getElementById('test-blog-detection').addEventListener('click', function() {
                testOutput.innerHTML = '';
                
                if (!window.visualEditor || !window.visualEditor.uiManager) {
                    testOutput.innerHTML = '<p class="error">Visual Editor not available</p>';
                    return;
                }
                
                const uiManager = window.visualEditor.uiManager;
                const isBlogPage = uiManager.isBlogPage();
                const blogCards = document.querySelectorAll('.blog-card');
                const blogHero = document.querySelector('.blog-hero-banner');
                
                testOutput.innerHTML = `
                    <p class="${isBlogPage ? 'success' : 'error'}">Blog page detected: ${isBlogPage}</p>
                    <p class="info">Blog cards found: ${blogCards.length}</p>
                    <p class="info">Blog hero found: ${blogHero ? 'Yes' : 'No'}</p>
                `;
            });
            
            // Test overlay creation
            document.getElementById('test-overlay-creation').addEventListener('click', function() {
                testOutput.innerHTML = '';
                
                if (!window.visualEditor || !window.visualEditor.uiManager) {
                    testOutput.innerHTML = '<p class="error">Visual Editor not available</p>';
                    return;
                }
                
                const uiManager = window.visualEditor.uiManager;
                
                // Remove existing overlays first
                uiManager.removeBlogOverlays();
                
                // Add blog overlays
                uiManager.addBlogOverlays();
                
                // Check if overlays were created
                const blogOverlays = document.querySelectorAll('.blog-edit-overlay');
                const heroOverlays = document.querySelectorAll('.blog-hero-overlay-edit');
                
                testOutput.innerHTML = `
                    <p class="${blogOverlays.length > 0 ? 'success' : 'error'}">Blog card overlays created: ${blogOverlays.length}</p>
                    <p class="${heroOverlays.length > 0 ? 'success' : 'error'}">Hero overlays created: ${heroOverlays.length}</p>
                `;
            });
            
            // Test routing
            document.getElementById('test-routing').addEventListener('click', function() {
                testOutput.innerHTML = '';
                
                if (!window.visualEditor || !window.visualEditor.uiManager) {
                    testOutput.innerHTML = '<p class="error">Visual Editor not available</p>';
                    return;
                }
                
                const uiManager = window.visualEditor.uiManager;
                
                // Test blog edit routing
                try {
                    // This should open blogWriter.html with edit parameter
                    // We'll just test the URL construction
                    const testBlogId = 'test-blog-id-123';
                    const expectedUrl = `/blogWriter.html?edit=${encodeURIComponent(testBlogId)}`;
                    
                    testOutput.innerHTML = `
                        <p class="success">Edit routing URL: ${expectedUrl}</p>
                        <p class="info">Add blog URL: /blogWriter.html</p>
                        <p class="success">Routing logic appears to be working</p>
                    `;
                } catch (error) {
                    testOutput.innerHTML = `<p class="error">Routing test failed: ${error.message}</p>`;
                }
            });
        });
    </script>
</body>
</html>
