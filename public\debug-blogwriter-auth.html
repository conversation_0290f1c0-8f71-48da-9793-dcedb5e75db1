<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Debug Blogwriter Authentication</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Debug Blogwriter Authentication</h1>
    
    <div class="test-section">
        <h2>Authentication Tests</h2>
        <button id="test-admin-endpoint" class="btn btn-primary">Test Admin Endpoint</button>
        <button id="test-blogwriter-endpoint" class="btn btn-primary">Test Blogwriter Endpoint</button>
        <button id="test-combined-role-check" class="btn btn-primary">Test Combined Role Check</button>
        <div id="auth-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Visual Editor Tests</h2>
        <button id="test-visual-editor-init" class="btn btn-primary">Test Visual Editor Init</button>
        <button id="test-ui-manager" class="btn btn-primary">Test UI Manager</button>
        <button id="check-edit-button" class="btn btn-primary">Check Edit Button</button>
        <div id="ve-results"></div>
    </div>
    
    <div class="test-section">
        <h2>User Management</h2>
        <button id="create-blogwriter" class="btn btn-primary">Create Blogwriter User</button>
        <div id="user-results"></div>
    </div>

    <div class="test-section">
        <h2>Quick Actions</h2>
        <a href="/login.html" class="btn btn-secondary">Go to Login</a>
        <a href="/blog" class="btn btn-secondary">Go to Blog</a>
        <button id="logout" class="btn btn-secondary">Logout</button>
    </div>

    <!-- Visual Editor Scripts -->
    <script src="/js/editor/editor-state.js"></script>
    <script src="/js/editor/api-service.js"></script>
    <script src="/js/editor/override-engine.js"></script>
    <script src="/js/editor/features/image-browser.js"></script>
    <script src="/js/editor/features/section-sorter.js"></script>
    <script src="/js/editor/ui-manager.js"></script>
    <script src="/js/visual-editor-v2.js"></script>

    <script>
        function updateResults(containerId, html) {
            document.getElementById(containerId).innerHTML = html;
        }

        // Test admin endpoint
        document.getElementById('test-admin-endpoint').addEventListener('click', async function() {
            try {
                const response = await fetch('/api/login?check=admin', { cache: 'no-store' });
                const data = response.ok ? await response.json() : null;
                updateResults('auth-results', `
                    <h3>Admin Endpoint Test</h3>
                    <p class="${response.ok ? 'success' : 'error'}">Status: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `);
            } catch (error) {
                updateResults('auth-results', `<p class="error">Admin endpoint error: ${error.message}</p>`);
            }
        });

        // Test blogwriter endpoint
        document.getElementById('test-blogwriter-endpoint').addEventListener('click', async function() {
            try {
                const response = await fetch('/api/protected?role=blogwriter', { cache: 'no-store' });
                const data = response.ok ? await response.json() : null;
                updateResults('auth-results', `
                    <h3>Blogwriter Endpoint Test</h3>
                    <p class="${response.ok ? 'success' : 'error'}">Status: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `);
            } catch (error) {
                updateResults('auth-results', `<p class="error">Blogwriter endpoint error: ${error.message}</p>`);
            }
        });

        // Test combined role check
        document.getElementById('test-combined-role-check').addEventListener('click', async function() {
            if (!window.apiService) {
                updateResults('auth-results', '<p class="error">API Service not available</p>');
                return;
            }
            
            try {
                const userRole = await window.apiService.checkUserRole();
                updateResults('auth-results', `
                    <h3>Combined Role Check</h3>
                    <pre>${JSON.stringify(userRole, null, 2)}</pre>
                `);
            } catch (error) {
                updateResults('auth-results', `<p class="error">Combined role check error: ${error.message}</p>`);
            }
        });

        // Test visual editor initialization
        document.getElementById('test-visual-editor-init').addEventListener('click', function() {
            const results = [];
            
            if (window.visualEditor) {
                results.push('<p class="success">✅ Visual Editor loaded</p>');
                
                if (window.visualEditor.userRole) {
                    const role = window.visualEditor.userRole;
                    results.push(`<p class="info">User Role: ${JSON.stringify(role)}</p>`);
                } else {
                    results.push('<p class="error">❌ User role not set</p>');
                }
                
                if (window.visualEditor.uiManager) {
                    results.push('<p class="success">✅ UI Manager available</p>');
                } else {
                    results.push('<p class="error">❌ UI Manager not available</p>');
                }
            } else {
                results.push('<p class="error">❌ Visual Editor not loaded</p>');
            }
            
            updateResults('ve-results', results.join(''));
        });

        // Test UI Manager
        document.getElementById('test-ui-manager').addEventListener('click', function() {
            if (!window.visualEditor || !window.visualEditor.uiManager) {
                updateResults('ve-results', '<p class="error">UI Manager not available</p>');
                return;
            }
            
            const uiManager = window.visualEditor.uiManager;
            const results = [];
            
            // Check if initialize was called
            results.push('<p class="info">UI Manager methods available:</p>');
            results.push(`<p class="info">- init: ${typeof uiManager.init}</p>`);
            results.push(`<p class="info">- createToggle: ${typeof uiManager.createToggle}</p>`);
            results.push(`<p class="info">- initialize: ${typeof uiManager.initialize}</p>`);
            
            updateResults('ve-results', results.join(''));
        });

        // Check edit button
        document.getElementById('check-edit-button').addEventListener('click', function() {
            const editButton = document.getElementById('edit-mode-toggle');
            const results = [];
            
            if (editButton) {
                results.push('<p class="success">✅ Edit mode toggle button found</p>');
                results.push(`<p class="info">Button text: "${editButton.textContent}"</p>`);
                results.push(`<p class="info">Button classes: "${editButton.className}"</p>`);
                results.push(`<p class="info">Button visible: ${editButton.offsetParent !== null}</p>`);
            } else {
                results.push('<p class="error">❌ Edit mode toggle button NOT found</p>');
                
                // Check if any buttons exist
                const allButtons = document.querySelectorAll('button');
                results.push(`<p class="info">Total buttons on page: ${allButtons.length}</p>`);
                
                // List all button IDs
                const buttonIds = Array.from(allButtons).map(btn => btn.id || 'no-id').join(', ');
                results.push(`<p class="info">Button IDs: ${buttonIds}</p>`);
            }
            
            updateResults('ve-results', results.join(''));
        });

        // Create blogwriter user
        document.getElementById('create-blogwriter').addEventListener('click', async function() {
            try {
                const response = await fetch('/api/create-blogwriter', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                if (response.ok) {
                    updateResults('user-results', `
                        <h3>Blogwriter User ${data.message.includes('already exists') ? 'Already Exists' : 'Created'}</h3>
                        <p class="success">Email: ${data.user.email}</p>
                        <p class="success">Name: ${data.user.name}</p>
                        <p class="success">Role: ${data.user.role}</p>
                        ${data.credentials ? `
                            <p class="info"><strong>Login Credentials:</strong></p>
                            <p class="info">Email: ${data.credentials.email}</p>
                            <p class="info">Password: ${data.credentials.password}</p>
                        ` : ''}
                    `);
                } else {
                    updateResults('user-results', `<p class="error">Error: ${data.message}</p>`);
                }
            } catch (error) {
                updateResults('user-results', `<p class="error">Error creating user: ${error.message}</p>`);
            }
        });

        // Logout (clear cookie)
        document.getElementById('logout').addEventListener('click', function() {
            // Clear the token cookie by setting it to expire in the past
            document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            alert('Logged out (cookie cleared)');
            location.reload();
        });

        // Auto-run some tests on page load
        setTimeout(() => {
            document.getElementById('test-visual-editor-init').click();
        }, 2000);
    </script>
</body>
</html>
