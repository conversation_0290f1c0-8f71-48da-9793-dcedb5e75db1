<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <title>Tutor Zone – Tutors Alliance Scotland</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/responsive-helper.js"></script>
    <script src="/js/nav-loader.js" defer=""></script>
    <script src="/js/dynamic-nav.js" defer=""></script>

    <script>
        /* Guard ‑ redirect non‑tutors to login */
        (async () => {
            try {
                const r = await fetch('/api/protected?role=tutor');
                if (!r.ok) throw new Error();
            } catch {
                location.href = '/login.html?role=tutor';
            }
        })();
    </script>
</head>
<body class="tutorzone-page" data-page="tutorszone" data-dyn-manual="true">
    <!-- Shared banner/header -->
    <header>
        <h1 data-ve-block-id="fdbc62d5-669b-4d8f-b468-56f94040b9c6">Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a href="/" class="banner-login-link login-box" data-ve-block-id="898d0856-01dd-4b4e-8953-4341a82f0632">Home</a>
            <a href="login.html?role=admin" class="banner-login-link login-box" data-ve-block-id="18b34fa5-9b45-4c94-81af-da63e9386c9f">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <!-- Rolling banner container -->
    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner">
            <!-- JS will populate tutor names/subjects here -->
        </div>
    </div>

    <main>
        <div class="mission-row">
            <!-- LEFT COLUMN: Shield + Ribbons -->
            <div class="left-col">
                <img src="/images/centralShield.png" alt="Large STA Shield" class="main-shield" id="imageShield" data-ve-block-id="ba919066-a165-49c8-aac4-a54c0a559fb3">
                <img src="/images/bannerWithRibbons.png" alt="Banner Ribbon" class="main-ribbons" id="imageBanner" data-ve-block-id="b5cb0ff9-f6c5-48ae-80fc-64cec4ea3dc2">
            </div>

            <!-- RIGHT COLUMN: heading + about-us text -->
            <div class="right-col">
                <div class="about-us-landing" id="aboutUsLanding">
                    <h1 class="mission-statement" data-ve-block-id="a00bea81-b559-44b8-b7a4-2efdb41d8e7c">Tutor Zone</h1>
                </div>
            </div>
        </div>

        <!-- ✦ TOP -->
        <section id="dynamicSectionsTop" class="dynamic-section-container" data-ve-section-id="dynamicSectionsTop"></section>

        <!-- TUTOR RESOURCES: Two-column content -->
        <section class="two-col-content fade-in-section">
            <div>
                <h2 data-ve-block-id="ecf10f0c-a69e-4e6e-839d-dc4f2074b30e">Resources for Tutors</h2>
                <p data-ve-block-id="a4f518ed-88a1-4cb7-96ad-d9ec68d3ffa1">
                    Find guides, tutorials, and other materials to help you excel as a tutor.
                    Our resources are designed to support your professional development and enhance
                    your tutoring practice.
                </p>
                <ul class="resource-list">
                    <li data-ve-block-id="li-tutoring-best-practices"><a href="#" data-ve-block-id="466c9384-53ca-4c40-acdb-4fc4f458f2f3">Tutoring Best Practices</a></li>
                    <li data-ve-block-id="li-professional-dev-guides"><a href="#" data-ve-block-id="7aa610ea-1b07-4171-95cb-93ea8dee2112">Professional Development Guides</a></li>
                    <li data-ve-block-id="li-resource-library"><a href="#" data-ve-block-id="43b7772f-ced1-48e8-8612-cd0b3e627736">Resource Library</a></li>
                </ul>
                <a href="#" class="button aurora" data-ve-block-id="373e409b-986b-4df9-887f-bafabe68ab6a">View All Resources</a>
            </div>
            <div>
                <img src="/images/tutorStatic1.PNG" alt="Tutoring resources" data-ve-block-id="995f4a91-2c66-460a-8684-19dd43c571a8">
            </div>
        </section>

        <!-- EVENTS SECTION: Full-width section -->
        <section class="tutor-zone-section fade-in-section">
            <div class="zone-gradient-bg tutor-gradient-bg">
                <div class="zone-list-row">
                    <div class="tutor-box">
                        <h2 data-ve-block-id="5b1cbcfb-6d97-4438-abc6-a1bd17aa4ac6">📅 Upcoming Events</h2>
                        <p data-ve-block-id="fdb62dcf-d90d-483d-8cdf-d6b6afade466">Keep up-to-date with upcoming webinars and training sessions to enhance your tutoring skills.</p>
                        <div class="events-list">
                            <div class="event-item">
                                <h3 data-ve-block-id="ed80c4b4-f122-4a10-9516-0be1ae8c3d0d">Effective Online Tutoring</h3>
                                <p data-ve-block-id="048c341a-fc48-444f-83f2-8f38182bce2b">June 15, 2023 | 7:00 PM</p>
                            </div>
                            <div class="event-item">
                                <h3 data-ve-block-id="af7cb74c-9b2c-4451-ac27-4c7b9243f5a6">Supporting Students with Learning Differences</h3>
                                <p data-ve-block-id="bb2b4844-0ef2-4b5a-9777-f21470a132e4">June 22, 2023 | 6:30 PM</p>
                            </div>
                        </div>
                        <a class="button aurora" href="#" data-ve-block-id="1e335c73-7129-4462-913b-aca6cfc54eb6">View All Events</a>
                    </div>
                    <img src="/images/legoRight.png" alt="Lego Right" class="tutor-img-list tutor-img-list-right" data-ve-block-id="895cbd05-0dc3-40f6-a7bf-099da7fc3cdd">
                </div>
            </div>
        </section>


        <!-- ✦ MIDDLE -->
        <section id="dynamicSectionsMiddle" class="dynamic-section-container" data-ve-section-id="dynamicSectionsMiddle"></section>

        <!-- CONNECT SECTION: Two-column content -->
        <section class="two-col-content fade-in-section">
            <div>
                <h2 data-ve-block-id="ac81f63f-233f-40f9-b48c-ef85a13c3ccf">Connect with Fellow Tutors</h2>
                <p data-ve-block-id="6fad5ace-a245-4247-9906-9de7ff8260e6">
                    Join our community of professional tutors to share experiences, resources, and best practices.
                    Connect with other tutors in your area or subject specialty.
                </p>
                <div class="social-connect">
                    <p data-ve-block-id="dae8a414-ba7e-4dd0-98cd-44bf66a306e9">Follow us on Facebook:</p>
                    <a href="https://www.facebook.com/TutorsAllianceScotland" target="_blank" class="button aurora" data-ve-block-id="a7a19180-fb4f-40f8-885a-5e2f9c2ae0f3">
                        Visit our Facebook Page
                    </a>
                </div>
            </div>
            <div>
                <img src="/images/tutorStatic2.PNG" alt="Connect with tutors" data-ve-block-id="e088be69-043a-484c-a607-f80210e28d12">
            </div>
        </section>

        <!-- TUTOR HIGHLIGHT: Full-width section -->
        <section class="tutor-zone-section fade-in-section">
            <div class="zone-gradient-bg tutor-gradient-bg">
                <div class="zone-list-row">
                    <img src="/images/legoLeft.png" alt="Lego Left" class="tutor-img-list tutor-img-list-left" style="top: 77px;" data-ve-block-id="c4d613d0-4989-4079-908b-29514ecff9b1">
                    <div class="tutor-box">
                        <h2 data-ve-block-id="0bfef991-b6cf-4282-a55b-8e702563d398">🌟 Tutor Highlight</h2>
                        <div class="tutor-highlight-content">
                            <img src="/images/tutorStatic3.PNG" alt="Featured Tutor" class="tutor-highlight-img" style="max-width: 150px; border-radius: 50%; margin: 0 auto 1rem auto; display: block;" data-ve-block-id="83102437-f3e9-4bc7-aba3-e89381f8999a">
                            <h3 data-ve-block-id="3a1d237d-937c-403b-8ae4-522ed953a844">Jane Doe</h3>
                            <p class="tutor-subjects" data-ve-block-id="78672032-c778-4cc1-a43a-5e52830075a4">English, Literature</p>
                            <p class="tutor-bio" data-ve-block-id="df2c67cf-78c1-41e1-9006-e5cd8d9707e1">
                                Jane has been tutoring for over 10 years and specializes in helping students
                                improve their writing skills and critical analysis. Her students consistently
                                achieve excellent results in their exams.
                            </p>
                            <a href="#" class="button aurora" data-ve-block-id="3be869ea-b7e4-474e-9550-fa810867a459">View Profile</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA BUTTONS WITH BACKGROUND -->
        <section class="cta-banner fade-in-section">
            <div class="cta-content">
                <div class="cta-buttons">
                    <a class="button aurora" href="#" data-ve-block-id="ed8e5109-cc37-43d5-9442-e1dcf7f07ed1">View Resources</a>
                    <a class="button aurora" href="#" data-ve-block-id="faa31872-ecfa-4888-9493-b4acb1160ed7">Upcoming Events</a>
                    <a class="button aurora" href="contact.html" data-ve-block-id="98039ade-8684-4ac4-962b-7c97417fa8a2">Contact Us</a>
                </div>
            </div>
        </section>

        <!-- Clear separator before dynamic sections -->
        <div class="dynamic-sections-separator"></div>


        <!-- ✦ BOTTOM -->
        <section id="dynamicSections" class="dynamic-section-container" data-ve-section-id="dynamicSections"></section>
    </main>

    <!-- SOCIAL ICONS FOOTER -->
    <footer class="site-footer fade-in-section">
        <div class="footer-icons">
            <a href="#" aria-label="Instagram" data-ve-block-id="dc6f2276-2909-4df1-b326-2836f678dded"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="Facebook" data-ve-block-id="fe6f4d0b-0d25-428a-90f7-120f4baf81d7"><i class="fab fa-facebook-f"></i></a>
        </div>
    </footer>

    <!-- STATIC BOTTOM FOOTER -->
    <footer class="static-footer">
        <div class="static-footer-container">
            <div class="static-footer-left">
                <h4 data-ve-block-id="f2130392-2bf1-47b5-8135-36f675563a17">Extra Information</h4>
                <ul>
                    <li data-ve-block-id="li-footer-governance-tz"><a href="tutoring-standards.html" data-ve-block-id="aa19ec11-5d7e-4840-845e-be2dc03625a4">The TAS Way: Governance and Guidance</a></li>
                    <li data-ve-block-id="li-footer-faq-tz"><a href="faq.html" data-ve-block-id="d45ac837-848c-42b6-8569-5a6f67a83656">FAQ's</a></li>
                    <li data-ve-block-id="li-footer-privacy-tz"><a href="privacy-policy.html" data-ve-block-id="************************************">Privacy Policy</a></li>
                    <li data-ve-block-id="li-footer-safeguarding-tz"><a href="safeguarding-policy.html" data-ve-block-id="abfd53f4-51fd-4a71-9ce0-8edc44b911b7">Safeguarding Policy</a></li>
                    <li data-ve-block-id="li-footer-terms-tz"><a href="terms-and-conditions.html" data-ve-block-id="c21957a2-2c52-4a36-b46d-7aa98194d363">Terms and Conditions</a></li>
                </ul>
                <div class="static-footer-copyright">
                    <p data-ve-block-id="88bdc5a4-33ea-4ffd-8bd8-6d5bc852a509">ALL RIGHTS RESERVED © Tutors Alliance Scotland 2025</p>
                </div>
                <div class="static-footer-credits">
                    <p data-ve-block-id="2eeec5a7-71c3-4118-a0d0-6a58e2427850">Website by <a href="#" target="_blank" data-ve-block-id="819d5063-857e-4a77-a2b4-452d64579f86">Tutors Alliance Scotland</a></p>
                </div>
            </div>
            <div class="static-footer-right">
                <div class="website-url">
                    <p data-ve-block-id="b218de67-fda6-432e-bae8-9796a93726e2">www.tutorsalliancescotland.org.uk</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">


    <script src="/js/rolling-banner.js" defer=""></script>   <!-- *uses* initRollingBanner -->
    <script type="module" src="/js/visual-editor-v2.js?v=20250101-CACHE-BUST&t=1735747200" defer=""></script>

    <!-- bump the version whenever you redeploy -->
    <script src="/js/dynamic-sections.js?v=20240530" type="module" defer=""></script>
<!-- Visual Editor Templates -->
<template id="ve-editor-modal-template">
    <div id="editor-modal" class="ve-modal-container">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Edit Content</h3>
                <button id="close-modal" class="close-btn" aria-label="Close modal">×</button>
            </div>
            <div class="modal-body">
                <form id="content-form" onsubmit="return false;">
                    <div class="form-group">
                        <label for="content-type">Content Type:</label>
                        <select id="content-type" class="form-control" disabled="">
                            <option value="text">Text</option>
                            <option value="html">HTML</option>
                            <option value="image">Image</option>
                            <option value="link">Link</option>
                        </select>
                    </div>
                    <div class="form-group" id="text-group">
                        <label for="content-text">Text Content:</label>
                        <textarea id="content-text" class="form-control" rows="8" placeholder="Enter your text content here..."></textarea>
                        
                        <!-- Button Management for Text Elements -->
                        <div class="text-button-management">
                            <h4>Add Button</h4>
                            <p class="help-text">Add an aurora-style button at the end of this text element</p>
                            
                            <div id="text-buttons-list" class="text-buttons-list">
                                <!-- Existing buttons will be listed here -->
                            </div>
                            
                            <div class="button-form" id="new-button-form" style="display: none;">
                                <div class="form-group">
                                    <label for="new-button-text">Button Text:</label>
                                    <input type="text" id="new-button-text" class="form-control" placeholder="Enter button text">
                                </div>
                                <div class="form-group">
                                    <label for="new-button-url">Button URL:</label>
                                    <input type="url" id="new-button-url" class="form-control" placeholder="https://example.com">
                                </div>
                                <div class="button-form-actions">
                                    <button type="button" id="save-new-button" class="btn btn-primary">Add Button</button>
                                    <button type="button" id="cancel-new-button" class="btn btn-secondary">Cancel</button>
                                </div>
                            </div>
                            
                            <button type="button" id="add-text-button" class="btn btn-secondary">+ Add Button</button>
                        </div>
                    </div>
                    <div class="form-group" id="html-group">
                        <label for="content-html">HTML Content:</label>
                        <textarea id="content-html" class="form-control" rows="10" placeholder="Enter your HTML content here..."></textarea>
                    </div>
                    <div class="form-group" id="image-group">
                        <label for="content-image">Image URL:</label>
                        <div class="image-input-group">
                            <input type="url" id="content-image" class="form-control" placeholder="Enter image URL or browse/upload below">
                            <button type="button" id="browse-btn" class="btn btn-secondary">Browse Images</button>
                        </div>
                        <div id="image-preview" style="display: none;"><img src="" alt="Preview" style="max-width: 200px; max-height: 200px;"></div>
                        <div class="upload-section">
                            <label for="image-upload">Or upload a new image:</label>
                            <input type="file" id="image-upload" accept="image/*" class="form-control">
                            <button type="button" id="upload-btn" class="btn btn-secondary">Upload Image</button>
                            <div id="upload-progress" style="display: none;">
                                <div class="progress-bar"><div class="progress-fill"></div></div>
                                <span class="progress-text">Uploading...</span>
                            </div>
                        </div>
                        <label for="image-alt">Alt Text:</label>
                        <input type="text" id="image-alt" class="form-control" placeholder="Describe the image for accessibility">
                    </div>
                    <div class="form-group" id="link-group">
                        <label for="link-url">Link URL:</label>
                        <input type="url" id="link-url" class="form-control" placeholder="https://example.com">
                        <label for="link-text">Link Text:</label>
                        <input type="text" id="link-text" class="form-control" placeholder="Enter the text to display">
                        <div class="form-check">
                            <input type="checkbox" id="link-is-button" class="form-check-input">
                            <label for="link-is-button" class="form-check-label">Style as button</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="preview-btn" class="btn btn-secondary">Preview</button>
                        <button type="button" id="save-btn" class="btn btn-primary">Save Changes</button>
                        <button type="button" id="restore-btn" class="btn btn-warning">Restore Original</button>
                    </div>
                </form>
            </div>
        </div>
        <div id="image-browser" class="image-browser" style="display: none;"></div>
    </div>
</template>

<template id="ve-image-browser-template">
    <div class="image-browser-header">
        <h4>Browse Images</h4>
        <button type="button" id="close-browser" class="close-btn" aria-label="Close image browser">×</button>
    </div>
    <div class="image-browser-content">
        <div class="image-browser-toolbar">
            <input type="text" id="image-search" placeholder="Search images..." class="form-control">
            <select id="image-sort" class="form-control">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name</option>
            </select>
        </div>
        <div id="image-grid" class="image-grid">
            <div class="loading-spinner"></div>
        </div>
        <div id="image-pagination" class="image-pagination">
            <button type="button" id="prev-page" class="btn btn-secondary" disabled="">Previous</button>
            <span id="page-info">Page 1</span>
            <button type="button" id="next-page" class="btn btn-secondary">Next</button>
        </div>
    </div>
</template>


</body></html>